In `ImageMultiloc/index.tsx`:

```jsx
  const handleOnAdd = async (imageFiles: UploadFile[]) => {
    setImageFiles(imageFiles);

    try {
      const response = await addContentBuilderImage(imageFiles[0].base64);
      setProp((props: Props) => {
        props.image = {
          dataCode: response.data.attributes.code,
          imageUrl: response.data.attributes.image_url,
        };
      });
    } catch {
      // Do nothing
    }
  };
```

For the file attachment widget, this would look like:

```jsx
  const handleOnAdd = async (file: File) => {
    // ...
    
    try {
        const response = await addFileAttachment(file.id, project);
        // or response = await addFileAttachment(file.id, layout);
        // ...
    } catch {
        // ...
    }
};
```


