Type application/x-ms-dos-executable is already registered as a variant of application/x-ms-dos-executable.
                               web_api_v1_project_file_attachments GET    /web_api/v1/projects/:project_id/file_attachments(.:format)                                                web_api/v1/files/file_attachments#index {:format=>:json, :followable=>"Project", :parent_param=>:project_id, :attachable_type=>"Project"}
                                                                   POST   /web_api/v1/projects/:project_id/file_attachments(.:format)                                                web_api/v1/files/file_attachments#create {:format=>:json, :followable=>"Project", :parent_param=>:project_id, :attachable_type=>"Project"}
                                        web_api_v1_file_attachment GET    /web_api/v1/file_attachments/:attachable_id(.:format)                                                      web_api/v1/files/file_attachments#show {:format=>:json, :followable=>"Project", :parent_param=>:project_id, :attachable_type=>"Project"}
                                                                   PATCH  /web_api/v1/file_attachments/:attachable_id(.:format)                                                      web_api/v1/files/file_attachments#update {:format=>:json, :followable=>"Project", :parent_param=>:project_id, :attachable_type=>"Project"}
                                                                   PUT    /web_api/v1/file_attachments/:attachable_id(.:format)                                                      web_api/v1/files/file_attachments#update {:format=>:json, :followable=>"Project", :parent_param=>:project_id, :attachable_type=>"Project"}
                                                                   DELETE /web_api/v1/file_attachments/:attachable_id(.:format)                                                      web_api/v1/files/file_attachments#destroy {:format=>:json, :followable=>"Project", :parent_param=>:project_id, :attachable_type=>"Project"}
                                       web_api_v1_file_attachments GET    /web_api/v1/file_attachments(.:format)                                                                     web_api/v1/files/file_attachments#index {:format=>:json}
                                                                   POST   /web_api/v1/file_attachments(.:format)                                                                     web_api/v1/files/file_attachments#create {:format=>:json}
                                                                   GET    /web_api/v1/file_attachments/:id(.:format)                                                                 web_api/v1/files/file_attachments#show {:format=>:json}
                                                                   PATCH  /web_api/v1/file_attachments/:id(.:format)                                                                 web_api/v1/files/file_attachments#update {:format=>:json}
                                                                   PUT    /web_api/v1/file_attachments/:id(.:format)                                                                 web_api/v1/files/file_attachments#update {:format=>:json}
                                                                   DELETE /web_api/v1/