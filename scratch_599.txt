[TAN-5277] Update `update_file_upload_fields` to support `FileAttachment` alongside legacy `FileUpload`

Similarly to what was done for the `WebApi::V1::FilesController` (in commit 9bfa59bcb4657d70aa958e220425cd3254c8f8ca), the PR refactor `update_file_upload_fields` to support `FileAttachment` alongside legacy `FileUpload` to ease the migration from legacy files (including `FileUpload`) to `FileAttachment` + `Files::File`.
